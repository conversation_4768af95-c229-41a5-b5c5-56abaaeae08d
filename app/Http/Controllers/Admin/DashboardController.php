<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Player;
use App\Models\Fixture;
use App\Models\Order;
use App\Models\Merchandise;
use App\Models\MediaGallery;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        // Get statistics for dashboard
        $stats = [
            'total_news' => News::count(),
            'published_news' => News::published()->count(),
            'draft_news' => News::where('status', 'draft')->count(),
            'total_players' => Player::count(),
            'upcoming_fixtures' => Fixture::upcoming()->count(),
            'total_orders' => Order::count(),
            'pending_orders' => Order::pending()->count(),
            'total_merchandise' => Merchandise::count(),
            'out_of_stock' => Merchandise::where('stock_quantity', 0)->count(),
            'total_media' => MediaGallery::count(),
            'total_users' => User::count(),
            'total_fans' => User::where('role', 'fan')->count(),
        ];

        // Recent activities
        $recentNews = News::with('author')->latest()->take(5)->get();
        $recentOrders = Order::with('user')->latest()->take(5)->get();
        $upcomingFixtures = Fixture::upcoming()->take(5)->get();

        // Monthly revenue (last 6 months)
        $monthlyRevenue = Order::selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(total_price) as revenue')
            ->where('status', 'paid')
            ->where('created_at', '>=', now()->subMonths(6))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'recentNews',
            'recentOrders',
            'upcomingFixtures',
            'monthlyRevenue'
        ));
    }
}
