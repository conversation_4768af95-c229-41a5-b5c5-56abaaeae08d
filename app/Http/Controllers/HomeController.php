<?php

namespace App\Http\Controllers;

use App\Models\News;
use App\Models\Fixture;
use App\Models\Sponsor;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        // Get latest news (3 articles)
        $latestNews = News::published()
            ->with('author')
            ->latest('published_at')
            ->take(3)
            ->get();

        // Get next upcoming fixture
        $nextFixture = Fixture::upcoming()
            ->first();

        // Get recent results (last 3 finished matches)
        $recentResults = Fixture::finished()
            ->take(3)
            ->get();

        // Get sponsors for carousel
        $sponsors = Sponsor::all();

        return view('home', compact(
            'latestNews',
            'nextFixture',
            'recentResults',
            'sponsors'
        ));
    }
}
