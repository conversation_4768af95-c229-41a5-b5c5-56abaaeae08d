<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MediaGallery extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'file_type',
        'file_url',
        'caption',
        'uploaded_by',
    ];

    /**
     * Get the user who uploaded the media
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scope for images
     */
    public function scopeImages($query)
    {
        return $query->where('file_type', 'image');
    }

    /**
     * Scope for videos
     */
    public function scopeVideos($query)
    {
        return $query->where('file_type', 'video');
    }

    /**
     * Get the full file URL
     */
    public function getFullFileUrlAttribute(): string
    {
        // If it's already a full URL, return as is
        if (filter_var($this->file_url, FILTER_VALIDATE_URL)) {
            return $this->file_url;
        }

        // Otherwise, assume it's a storage path
        return asset('storage/' . $this->file_url);
    }

    /**
     * Check if media is an image
     */
    public function getIsImageAttribute(): bool
    {
        return $this->file_type === 'image';
    }

    /**
     * Check if media is a video
     */
    public function getIsVideoAttribute(): bool
    {
        return $this->file_type === 'video';
    }

    /**
     * Get thumbnail URL for videos
     */
    public function getThumbnailUrlAttribute(): string
    {
        if ($this->is_video) {
            // For YouTube videos, extract video ID and get thumbnail
            if (strpos($this->file_url, 'youtube.com') !== false || strpos($this->file_url, 'youtu.be') !== false) {
                preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $this->file_url, $matches);
                if (isset($matches[1])) {
                    return "https://img.youtube.com/vi/{$matches[1]}/maxresdefault.jpg";
                }
            }
            return asset('images/video-placeholder.jpg');
        }

        return $this->full_file_url;
    }
}
