<x-layouts.main>
    <x-slot name="title">Home</x-slot>
    <x-slot name="description">Welcome to Mbuni FC - Arusha's premier football club. Get the latest news, fixtures, and updates.</x-slot>

    <!-- Hero Section -->
    <section class="hero-gradient text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        Welcome to <span class="text-yellow-300">Mbuni FC</span>
                    </h1>
                    <p class="text-xl mb-8 text-gray-100">
                        Arusha's premier football club, bringing passion, excellence, and community spirit to the beautiful game.
                    </p>
                    
                    @if($nextFixture)
                        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8">
                            <h3 class="text-lg font-semibold mb-2">Next Match</h3>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-2xl font-bold">{{ $nextFixture->home_or_away === 'home' ? 'Mbuni FC' : $nextFixture->opponent }}</p>
                                    <p class="text-sm text-gray-200">{{ $nextFixture->home_or_away === 'home' ? 'vs' : 'at' }}</p>
                                    <p class="text-2xl font-bold">{{ $nextFixture->home_or_away === 'home' ? $nextFixture->opponent : 'Mbuni FC' }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-semibold">{{ $nextFixture->match_date->format('M d, Y') }}</p>
                                    <p class="text-sm text-gray-200">{{ $nextFixture->match_date->format('H:i') }}</p>
                                    <p class="text-sm text-gray-200">{{ $nextFixture->venue }}</p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="{{ route('tickets.index') }}" class="btn-primary bg-yellow-500 hover:bg-yellow-600 text-center">
                            Get Tickets
                        </a>
                        <a href="{{ route('news.index') }}" class="btn-outline border-white text-white hover:bg-white hover:text-primary-600 text-center">
                            Latest News
                        </a>
                    </div>
                </div>
                
                <div class="relative">
                    <img src="{{ asset('images/hero-team.jpg') }}" alt="Mbuni FC Team" class="rounded-lg shadow-2xl">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-lg"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Latest News Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Latest News</h2>
                <p class="text-xl text-gray-600">Stay updated with the latest from Mbuni FC</p>
            </div>

            @if($latestNews->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    @foreach($latestNews as $article)
                        <article class="card hover:shadow-lg transition-shadow duration-300">
                            @if($article->image)
                                <img src="{{ $article->image_url }}" alt="{{ $article->title }}" class="w-full h-48 object-cover">
                            @endif
                            <div class="card-body">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="badge badge-primary">{{ $article->category_name }}</span>
                                    <span class="text-sm text-gray-500">{{ $article->published_at->format('M d, Y') }}</span>
                                </div>
                                <h3 class="text-xl font-semibold mb-2 hover:text-primary-600">
                                    <a href="{{ route('news.show', $article) }}">{{ $article->title }}</a>
                                </h3>
                                <p class="text-gray-600 mb-4">{{ $article->excerpt }}</p>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-500">By {{ $article->author->name }}</span>
                                    <a href="{{ route('news.show', $article) }}" class="text-primary-600 hover:text-primary-700 font-medium">
                                        Read More →
                                    </a>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>

                <div class="text-center mt-12">
                    <a href="{{ route('news.index') }}" class="btn-primary">View All News</a>
                </div>
            @else
                <div class="text-center py-12">
                    <p class="text-gray-500 text-lg">No news articles available at the moment.</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Recent Results Section -->
    @if($recentResults->count() > 0)
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Recent Results</h2>
                    <p class="text-xl text-gray-600">Our latest match results</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($recentResults as $result)
                        <div class="card text-center">
                            <div class="card-body">
                                <div class="text-sm text-gray-500 mb-2">{{ $result->competition }}</div>
                                <div class="text-sm text-gray-500 mb-4">{{ $result->match_date->format('M d, Y') }}</div>
                                
                                <div class="flex items-center justify-between mb-4">
                                    <div class="text-center">
                                        <div class="font-semibold">{{ $result->home_or_away === 'home' ? 'Mbuni FC' : $result->opponent }}</div>
                                    </div>
                                    <div class="text-2xl font-bold text-primary-600">
                                        {{ $result->result }}
                                    </div>
                                    <div class="text-center">
                                        <div class="font-semibold">{{ $result->home_or_away === 'home' ? $result->opponent : 'Mbuni FC' }}</div>
                                    </div>
                                </div>
                                
                                <div class="text-sm text-gray-500">{{ $result->venue }}</div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-12">
                    <a href="{{ route('fixtures.index') }}" class="btn-primary">View All Fixtures</a>
                </div>
            </div>
        </section>
    @endif

    <!-- Sponsors Section -->
    @if($sponsors->count() > 0)
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Partners</h2>
                    <p class="text-xl text-gray-600">Proud to be supported by these amazing partners</p>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
                    @foreach($sponsors as $sponsor)
                        <div class="text-center">
                            @if($sponsor->has_website)
                                <a href="{{ $sponsor->formatted_website_url }}" target="_blank" class="block hover:opacity-75 transition-opacity">
                                    <img src="{{ $sponsor->logo_url }}" alt="{{ $sponsor->name }}" class="h-16 mx-auto object-contain">
                                </a>
                            @else
                                <img src="{{ $sponsor->logo_url }}" alt="{{ $sponsor->name }}" class="h-16 mx-auto object-contain">
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Call to Action Section -->
    <section class="py-16 hero-gradient text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">Join the Mbuni Family</h2>
            <p class="text-xl mb-8 text-gray-100">
                Be part of our journey and support your local team. Get exclusive merchandise, match tickets, and stay connected with the club.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('store.index') }}" class="btn-primary bg-yellow-500 hover:bg-yellow-600">
                    Shop Merchandise
                </a>
                <a href="{{ route('contact.index') }}" class="btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                    Contact Us
                </a>
            </div>
        </div>
    </section>
</x-layouts.main>
